import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import OpenAI from 'openai';
import sharp from 'sharp';
import { enforceRateLimit, decrementLimits, logGenerationAttempt } from '@/lib/rateLimiter';
import { processImageWithLogo } from '@/lib/imageUtils';
import { sanitizeImagePrompt } from '@/lib/inputSanitization';
import path from 'path';

// OpenAI client will be instantiated in the handler to avoid build-time issues

export async function POST(request: NextRequest) {
  let userId: number = 0;
  let rateLimitStatus: { 
    userRemaining: number;
    globalRemaining: number;
    userLimit: number;
    globalLimit: number;
  } | null = null;

  try {
    // Initialize OpenAI client with security configurations
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
      timeout: 60000, // 60 second timeout
      maxRetries: 3,
      defaultHeaders: {
        'User-Agent': 'CryBaby/1.0'
      }
    });

    // Check authentication
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('🔐 Authenticated user:', user.id);

    // Check rate limits before processing
    console.log('🛡️ Checking rate limits...');
    const rateLimitResult = await enforceRateLimit(user.id);
    userId = rateLimitResult.userId;
    rateLimitStatus = rateLimitResult.status;

    if (!rateLimitResult.allowed) {
      console.log('❌ Rate limit exceeded:', rateLimitResult.status.reason);

      // Log the blocked attempt
      await logGenerationAttempt({
        userId,
        prompt: 'Rate limit exceeded',
        success: false,
        errorMessage: rateLimitResult.status.reason
      });

      return NextResponse.json({
        error: rateLimitResult.status.reason,
        rateLimitStatus: rateLimitResult.status
      }, { status: 429 });
    }

    console.log('✅ Rate limit check passed. Remaining: User=' + rateLimitStatus.userRemaining + ', Global=' + rateLimitStatus.globalRemaining);

    const { imageData, prompt } = await request.json();

    if (!imageData || !prompt) {
      return NextResponse.json({ 
        error: 'Image data and prompt are required' 
      }, { status: 400 });
    }

    // Sanitize and validate prompt
    const sanitizationResult = sanitizeImagePrompt(prompt);
    const sanitizedPrompt = sanitizationResult.sanitized;

    // Log warnings if any
    if (sanitizationResult.warnings.length > 0) {
      console.warn('🚨 Prompt sanitization warnings:', sanitizationResult.warnings);
    }

    // Validate prompt length for gpt-image-1
    if (sanitizedPrompt.length > 32000) {
      return NextResponse.json({
        error: 'Prompt too long. Maximum 32000 characters for gpt-image-1.'
      }, { status: 400 });
    }

    if (sanitizedPrompt.length === 0) {
      return NextResponse.json({
        error: 'Invalid prompt after sanitization.'
      }, { status: 400 });
    }

    // Convert base64 image to buffer and ensure it's in the right format
    const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, '');
    const imageBuffer = Buffer.from(base64Data, 'base64');

    // Convert to PNG format using Sharp for better compatibility
    const pngBuffer = await sharp(imageBuffer)
      .png()
      .resize(1024, 1024, { 
        fit: 'inside', 
        withoutEnlargement: true,
        background: { r: 255, g: 255, b: 255, alpha: 1 }
      })
      .toBuffer();

    // Create a proper File stream for OpenAI
    const imageBlob = new Blob([pngBuffer], { type: 'image/png' });
    const imageFile = new File([imageBlob], 'image.png', { type: 'image/png' });

    console.log('Sending image to OpenAI:', {
      originalSize: imageBuffer.length,
      processedSize: pngBuffer.length,
      prompt: prompt,
      fileType: imageFile.type
    });

    // Use configurable model for image editing (defaults to gpt-image-1)
    const imageModel = process.env.IMAGE_MODEL || "gpt-image-1";
    console.log(`Using ${imageModel} for image transformation...`);
    const response = await openai.images.edit({
      model: imageModel,
      image: imageFile,
      prompt: prompt,
      n: 1,
      size: "1024x1024",
      output_format: "png",
      input_fidelity: "high",
      quality: "medium"
    });
    
    const modelUsed = imageModel;

    console.log('Model used:', modelUsed);
    console.log('Response data length:', response.data?.length);

    let editedImageUrl = null;
    
    if (response.data && Array.isArray(response.data) && response.data.length > 0) {
      const firstResult = response.data[0];
      
      // gpt-image-1 returns base64-encoded images
      if (firstResult.b64_json) {
        // Convert base64 to data URL
        editedImageUrl = `data:image/png;base64,${firstResult.b64_json}`;
        console.log('Generated data URL from base64 for gpt-image-1');
      }
    }

    if (!editedImageUrl) {
      console.error('No image data found in OpenAI response. Full response:', JSON.stringify(response, null, 2));

      // Log the failed generation attempt
      await logGenerationAttempt({
        userId,
        prompt,
        success: false,
        errorMessage: `No edited image data found. Model: ${modelUsed}`
      });

      throw new Error(`No edited image data found. Model: ${modelUsed}, Response keys: ${response.data?.[0] ? Object.keys(response.data[0]).join(', ') : 'no data'}`);
    }

    // Generation was successful - decrement the rate limits
    console.log('✅ Generation successful, decrementing rate limits...');
    const decrementSuccess = await decrementLimits(userId);

    if (!decrementSuccess) {
      console.error('⚠️ Failed to decrement rate limits after successful generation');
      // Continue anyway since the generation was successful
    }

    // Log the successful generation attempt
    await logGenerationAttempt({
      userId,
      prompt,
      success: true,
      imageUrl: editedImageUrl
    });

    console.log('🎉 Generation completed successfully');

    // Add logo overlay to the processed image
    console.log('Adding logo overlay to processed image...');
    const logoPath = path.join(process.cwd(), 'public', 'extended-logo.svg');

    try {
      const imageWithLogo = await processImageWithLogo(editedImageUrl, logoPath, {
        logoSizePercent: 12,  // 12% of image width
        paddingPercent: 3,    // 3% padding from edges
        opacity: 0.9          // 90% opacity for clear visibility
      });

      console.log('Logo overlay added successfully');
      editedImageUrl = imageWithLogo;

    } catch (logoError) {
      console.error('Failed to add logo overlay:', logoError);
      // Continue without logo if overlay fails - don't break the main functionality
      console.log('Continuing without logo overlay due to error');
    }

    return NextResponse.json({
      success: true,
      editedImageUrl,
      originalPrompt: prompt,
      processedAt: new Date().toISOString(),
      model: modelUsed,
      rateLimitStatus: {
        userRemaining: Math.max(0, rateLimitStatus.userRemaining - 1),
        globalRemaining: Math.max(0, rateLimitStatus.globalRemaining - 1),
        userLimit: rateLimitStatus.userLimit,
        globalLimit: rateLimitStatus.globalLimit
      }
    });

  } catch (error) {
    console.error('Processing error:', error);

    // Log the failed generation attempt if we have user info
    if (userId > 0) {
      await logGenerationAttempt({
        userId,
        prompt: 'Error occurred during processing',
        success: false,
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Handle specific OpenAI errors
    if (error instanceof OpenAI.APIError) {
      console.error('🔥 OpenAI API Error:', {
        status: error.status,
        message: error.message,
        userId: userId
      });

      if (error.status === 401) {
        return NextResponse.json({
          error: 'Service temporarily unavailable. Please try again later.'
        }, { status: 500 });
      }
      if (error.status === 429) {
        return NextResponse.json({
          error: 'Service is busy. Please try again in a few minutes.'
        }, { status: 429 });
      }
      if (error.status === 400) {
        return NextResponse.json({
          error: 'Invalid image or prompt. Please check your input and try again.'
        }, { status: 400 });
      }
      return NextResponse.json({
        error: 'Processing failed. Please try again later.'
      }, { status: 500 });
    }

    return NextResponse.json({
      error: 'Processing failed. Please try again.'
    }, { status: 500 });
  }
}